{% extends "base.html" %}

{% block styles %}
  <link rel="stylesheet" href="{{ url_for('static', filename='css/extra.css') }}" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" />
  <style>
    .templates-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .templates-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 12px;
      margin-bottom: 30px;
      text-align: center;
    }
    
    .templates-header h1 {
      margin: 0;
      font-size: 2.5rem;
      font-weight: 300;
    }
    
    .templates-header p {
      margin: 10px 0 0 0;
      opacity: 0.9;
      font-size: 1.1rem;
    }
    
    .templates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 25px;
      margin-bottom: 30px;
    }
    
    .template-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      overflow: hidden;
      border: 1px solid #e1e5e9;
    }
    
    .template-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .template-card-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 20px;
      border-bottom: 1px solid #dee2e6;
    }
    
    .template-name {
      font-size: 1.4rem;
      font-weight: 600;
      color: #2c3e50;
      margin: 0 0 8px 0;
    }
    
    .template-id {
      font-size: 0.85rem;
      color: #6c757d;
      font-family: 'Courier New', monospace;
      background: #f8f9fa;
      padding: 2px 6px;
      border-radius: 4px;
      display: inline-block;
    }
    
    .template-card-body {
      padding: 20px;
    }
    
    .template-identifiers {
      margin-bottom: 15px;
    }
    
    .template-identifiers h4 {
      font-size: 0.9rem;
      color: #495057;
      margin: 0 0 8px 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }
    
    .identifier-tag {
      display: inline-block;
      background: #e3f2fd;
      color: #1976d2;
      padding: 4px 10px;
      border-radius: 20px;
      font-size: 0.8rem;
      margin: 2px 4px 2px 0;
      border: 1px solid #bbdefb;
    }
    
    .template-actions {
      display: flex;
      gap: 10px;
      margin-top: 15px;
    }
    
    .btn-view-details {
      flex: 1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-view-details:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }
    
    .template-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(5px);
    }
    
    .modal-content {
      background-color: #fff;
      margin: 2% auto;
      padding: 0;
      border-radius: 12px;
      width: 90%;
      max-width: 900px;
      max-height: 90vh;
      overflow: hidden;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }
    
    .modal-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .modal-header h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
    }
    
    .close {
      color: white;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
      transition: opacity 0.3s ease;
    }
    
    .close:hover {
      opacity: 0.7;
    }
    
    .modal-body {
      padding: 30px;
      max-height: calc(90vh - 140px);
      overflow-y: auto;
    }
    
    .detail-section {
      margin-bottom: 25px;
    }
    
    .detail-section h3 {
      color: #2c3e50;
      font-size: 1.1rem;
      margin: 0 0 10px 0;
      padding-bottom: 5px;
      border-bottom: 2px solid #e9ecef;
      font-weight: 600;
    }
    
    .detail-content {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 15px;
      border-left: 4px solid #667eea;
    }
    
    .json-container {
      background: #2d3748;
      border-radius: 6px;
      overflow: hidden;
    }
    
    .json-container pre {
      margin: 0;
      padding: 20px;
      overflow-x: auto;
      font-size: 0.9rem;
      line-height: 1.5;
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
    }
    
    .empty-state i {
      font-size: 4rem;
      margin-bottom: 20px;
      opacity: 0.5;
    }
    
    .empty-state h3 {
      margin: 0 0 10px 0;
      color: #495057;
    }
    
    .template-type-badge {
      background: #28a745;
      color: white;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;
      margin-left: 10px;
    }
  </style>
{% endblock %}

{% block scripts %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Modal functionality
      const modal = document.getElementById('templateModal');
      const closeBtn = document.querySelector('.close');
      
      // Close modal when clicking the X
      closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
      });
      
      // Close modal when clicking outside
      window.addEventListener('click', function(event) {
        if (event.target === modal) {
          modal.style.display = 'none';
        }
      });
      
      // Close modal with Escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
          modal.style.display = 'none';
        }
      });
    });
    
    function viewTemplateDetails(templateData) {
      const modal = document.getElementById('templateModal');
      const modalTitle = document.getElementById('modalTitle');
      const modalBody = document.getElementById('modalBody');
      
      // Parse template data
      const template = JSON.parse(templateData);
      
      // Set modal title
      modalTitle.textContent = template.name;
      
      // Build modal content
      let content = `
        <div class="detail-section">
          <h3>Template Information</h3>
          <div class="detail-content">
            <p><strong>ID:</strong> ${template.id}</p>
            <p><strong>Name:</strong> ${template.name}</p>
            <p><strong>Type:</strong> ${template.type || 'Not specified'}</p>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>Identifiers</h3>
          <div class="detail-content">
            ${template.identifiers && template.identifiers.length > 0 
              ? template.identifiers.map(id => `<span class="identifier-tag">${id}</span>`).join(' ')
              : '<em>No identifiers specified</em>'
            }
          </div>
        </div>
        
        <div class="detail-section">
          <h3>Prompt</h3>
          <div class="detail-content">
            <p>${template.prompt || '<em>No prompt specified</em>'}</p>
          </div>
        </div>
        
        <div class="detail-section">
          <h3>Response Schema</h3>
          <div class="json-container">
            <pre><code class="language-json">${JSON.stringify(template.response_schema, null, 2)}</code></pre>
          </div>
        </div>
      `;
      
      modalBody.innerHTML = content;
      
      // Re-run Prism highlighting
      Prism.highlightAll();
      
      // Show modal
      modal.style.display = 'block';
    }
  </script>
{% endblock %}

{% block content %}
<div class="templates-container">
  <div class="templates-header">
    <h1>Template Visualization</h1>
    <p>View and manage AI processing templates</p>
  </div>
  
  {% if templates and templates|length > 0 %}
    <div class="templates-grid">
      {% for template in templates %}
        <div class="template-card">
          <div class="template-card-header">
            <h3 class="template-name">
              {{ template.name }}
              {% if template.type %}
                <span class="template-type-badge">{{ template.type }}</span>
              {% endif %}
            </h3>
            <span class="template-id">ID: {{ template.id }}</span>
          </div>
          
          <div class="template-card-body">
            <div class="template-identifiers">
              <h4>Identifiers</h4>
              {% if template.identifiers and template.identifiers|length > 0 %}
                {% for identifier in template.identifiers %}
                  <span class="identifier-tag">{{ identifier }}</span>
                {% endfor %}
              {% else %}
                <em style="color: #6c757d;">No identifiers</em>
              {% endif %}
            </div>
            
            <div class="template-actions">
              <button class="btn-view-details" onclick="viewTemplateDetails('{{ template|tojson|e }}')">
                View Details
              </button>
            </div>
          </div>
        </div>
      {% endfor %}
    </div>
  {% else %}
    <div class="empty-state">
      <i class="ri-file-text-line"></i>
      <h3>No Templates Found</h3>
      <p>There are currently no templates available in the system.</p>
    </div>
  {% endif %}
</div>

<!-- Modal -->
<div id="templateModal" class="template-modal">
  <div class="modal-content">
    <div class="modal-header">
      <h2 id="modalTitle">Template Details</h2>
      <span class="close">&times;</span>
    </div>
    <div class="modal-body" id="modalBody">
      <!-- Content will be populated by JavaScript -->
    </div>
  </div>
</div>
{% endblock %}
