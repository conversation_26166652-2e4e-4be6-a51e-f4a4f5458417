<!DOCTYPE html>
<html lang="en">
<head>
    <title>AirTanker</title>
    <meta charset="UTF-8">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&amp;display=swap" rel="stylesheet">
    <link href="https://unpkg.com/css-pro-layout@1.1.0/dist/css/css-pro-layout.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.2.0/fonts/remixicon.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.datatables.net/2.1.8/css/dataTables.bootstrap5.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/base.css') }}" type="text/css" rel="stylesheet">
    {% block styles %}{% endblock %}    
    <!-- Date Picker Overrides -->
    <style>
      .date-picker-container {
        text-align: center;
        margin-top: 20px;
      }
        
      .date-input {
        width: auto;
        display: inline-block; /* To center in the div */
      }

        /* Additional styling for the date picker - make it "cool" */
      .date-picker {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        text-align: center;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
      }

      .date-picker:hover {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    </style>    
  </head>
  <body>
      <div class="layout has-sidebar fixed-sidebar fixed-header">
            <aside id="sidebar" class="sidebar break-point-sm has-bg-image">
              <a id="btn-collapse" style="text-decoration: none;" class="sidebar-collapser"><i class="ri-arrow-left-s-line" style="fill: red; text-decoration: none;"></i></a>
              <div class="image-wrapper">
                <img src="{{ url_for('static', filename='assets/favicon-32x32.png') }}" alt="sidebar background" />
              </div>
              <div class="sidebar-layout">
                <div class="sidebar-header">
                  <div class="pro-sidebar-logo">
                    <div style="background-color: transparent;"><img src="{{ url_for('static', filename='assets/favicon-32x32.png') }}"/></div>
                    <h5 style="color: #7d84ab;">AirTanker</h5>
                  </div>
                </div>
                <div class="sidebar-content">
                  <nav class="menu open-current-submenu">
                    <ul>
                      <li class="menu-header"><span> GENERAL </span></li>
                      <li class="menu-item">
                          <a href="/">
                            <span class="menu-icon">
                              <i class="ri-home-2-fill"></i>
                            </span>
                            <span class="menu-title">Home</span>
                          </a>
                        </li>
                      <li class="menu-item sub-menu">
                        <a href="#">
                          <span class="menu-icon">
                            <i class="ri-vip-diamond-fill"></i>
                          </span>
                          <span class="menu-title">Wizards</span>
                        </a>
                        <div class="sub-menu-list">
                          <ul>
                            <li class="menu-item">
                              <a href="/import_work_orders">
                                <span class="menu-title">Timesheet Wizard</span>
                              </a>
                            </li>
                            <li class="menu-item">
                              <a href="/import_work_orders_expenses">
                                <span class="menu-title">Expense Wizard</span>                                
                              </a>
                            </li>
                            <li class="menu-item">
                              <a href="/import_fixed_time">
                                <span class="menu-title">Fixed-Bid Time Wizard</span>
                                <!-- <span class="menu-suffix">
                                  <span class="badge secondary">Beta</span>
                                </span> -->
                              </a>
                            </li>
                          </ul>
                        </div>
                      </li>
                      <li class="menu-item sub-menu">
                        <a href="#">
                          <span class="menu-icon">
                            <i class="ri-check-fill"></i>
                          </span>
                          <span class="menu-title">Approvals</span>
                        </a>
                        <div class="sub-menu-list">
                          <ul>
                            <li class="menu-item">
                              <a href="/approvals/timesheets">
                                <span class="menu-title">Timesheet Approvals</span>
                              </a>
                            </li>
                            <li class="menu-item">
                              <a href="/approvals/expenses">
                                <span class="menu-title">Expense Approvals</span>
                                <span class="menu-suffix">
                                  <span class="badge secondary">Beta</span>
                                </span>
                              </a>
                            </li>
                          </ul>
                        </div>
                      </li>
                      {% if session.get('member_of') == 'finances' %}
                      <li class="menu-item">
                          <a href="/exports">
                            <span class="menu-icon">
                              <i class="ri-download-line"></i>
                            </span>
                            <span class="menu-title">Exports</span>
                            <!-- <span class="menu-suffix">
                              <span class="badge secondary">Beta</span>
                            </span> -->
                          </a>
                        </li>
                        <li class="menu-item">
                          <a href="/dashboard">
                            <span class="menu-icon">
                              <i class="ri-dashboard-fill"></i>                            
                            </span>
                            <span class="menu-title">Dashboard</span>
                          </a>
                        </li>
                      {% endif %}
                      <li class="menu-header" style="padding-top: 20px"><span> INFORMATION </span></li>
                      <li class="menu-item">
                        <a href="/edit-files">
                          <span class="menu-icon">
                            <i class="ri-file-edit-line"></i>
                          </span>
                          <span class="menu-title">Edit Files</span>
                        </a>
                      </li>
                      <li class="menu-item">
                        <a href="/logs">
                          <span class="menu-icon">
                            <i class="ri-file-list-3-line"></i>
                          </span>
                          <span class="menu-title">Logs</span>
                        </a>
                      </li>
                      <li class="menu-item">
                        <a href="/faqs">
                          <span class="menu-icon">
                            <i class="ri-questionnaire-line"></i>
                          </span>
                          <span class="menu-title">FAQs</span>
                        </a>
                      </li>
                      <li class="menu-item">
                        <a href="/releases">
                            <span class="menu-icon">
                                <i class="ri-calendar-line"></i>
                            </span>
                            <span class="menu-title">Releases</span>
                        </a>
                      </li>
                      <li class="menu-item">
                        <a href="/logout">
                          <span class="menu-icon">
                            <i class="ri-logout-box-r-line"></i>
                          </span>
                          <span class="menu-title">Logout</span>
                        </a>
                      </li>
                      {% if request.args.get('debug') == '1' or not config.get('PRODUCTION', True) %}
                      <li class="menu-item">
                        <a href="/admin/templates">
                          <span class="menu-icon">
                            <i class="ri-file-code-line"></i>
                          </span>
                          <span class="menu-title">Templates</span>
                        </a>
                      </li>
                      <li class="menu-item">
                        <a href="/admin/settings">
                          <span class="menu-icon">
                            <i class="ri-admin-line"></i>
                          </span>
                          <span class="menu-title">Admin Settings</span>
                        </a>
                      </li>
                      {% endif %}
                      <div class="sidebar-version">
                        <li class="menu-item">
                          <a>
                            <span style="font-size: 15px;" class="menu-title">Version 1.2.2</span>
                          </a>
                        </li>
                      </div>
                    </ul>
                  </nav>
                </div>
              </div>
            </aside>
            <div id="overlay" class="overlay"></div>
            <div class="layout">
              <main class="content">
                <div>
                  <a id="btn-toggle" href="#" class="sidebar-toggler break-point-sm">
                    <i class="ri-menu-line ri-xl"></i>
                  </a>
                  {% block content %}{% endblock %}                  
              </main>
              <div class="overlay"></div>
            </div>
          </div>
    {% block modal %} {% endblock %}     
  </body>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" integrity="sha384-I7E8VVD/ismYTF4hNIPjVp/Zjvgyol6VFvRkX/vR+Vc4jQkC+hVqc2pM8ODewa9r" crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.min.js" integrity="sha384-0pUGZvbkm6XF6gxjEnlmuGrJXVbNuzT9qBBavbLwCsOGabYfZo0T0to5eqruptLy" crossorigin="anonymous"></script>
  <script src="https://cdn.datatables.net/2.1.8/js/dataTables.js"></script>
  <script src="https://cdn.datatables.net/2.1.8/js/dataTables.bootstrap5.js"></script>
  {% block scripts %}{% endblock %}
  <script>
    function _defineProperty(obj, key, value) {if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}const ANIMATION_DURATION = 300;

    const SIDEBAR_EL = document.getElementById("sidebar");

    const SUB_MENU_ELS = document.querySelectorAll(
    ".menu > ul > .menu-item.sub-menu");


    const FIRST_SUB_MENUS_BTN = document.querySelectorAll(
    ".menu > ul > .menu-item.sub-menu > a");


    const INNER_SUB_MENUS_BTN = document.querySelectorAll(
    ".menu > ul > .menu-item.sub-menu .menu-item.sub-menu > a");


    class PopperObject {




    constructor(reference, popperTarget) {_defineProperty(this, "instance", null);_defineProperty(this, "reference", null);_defineProperty(this, "popperTarget", null);
        this.init(reference, popperTarget);
    }

    init(reference, popperTarget) {
        this.reference = reference;
        this.popperTarget = popperTarget;
        this.instance = Popper.createPopper(this.reference, this.popperTarget, {
        placement: "right",
        strategy: "fixed",
        resize: true,
        modifiers: [
        {
            name: "computeStyles",
            options: {
            adaptive: false } },


        {
            name: "flip",
            options: {
            fallbackPlacements: ["left", "right"] } }] });





        document.addEventListener(
        "click",
        e => this.clicker(e, this.popperTarget, this.reference),
        false);


        const ro = new ResizeObserver(() => {
        this.instance.update();
        });

        ro.observe(this.popperTarget);
        ro.observe(this.reference);
    }

    clicker(event, popperTarget, reference) {
        if (
        SIDEBAR_EL.classList.contains("collapsed") &&
        !popperTarget.contains(event.target) &&
        !reference.contains(event.target))
        {
        this.hide();
        }
    }

    hide() {
        this.instance.state.elements.popper.style.visibility = "hidden";
    }}


    class Poppers {


    constructor() {_defineProperty(this, "subMenuPoppers", []);
        this.init();
    }

    init() {
        SUB_MENU_ELS.forEach(element => {
        this.subMenuPoppers.push(
        new PopperObject(element, element.lastElementChild));

        this.closePoppers();
        });
    }

    togglePopper(target) {
        if (window.getComputedStyle(target).visibility === "hidden")
        target.style.visibility = "visible";else
        target.style.visibility = "hidden";
    }

    updatePoppers() {
        this.subMenuPoppers.forEach(element => {
        element.instance.state.elements.popper.style.display = "none";
        element.instance.update();
        });
    }

    closePoppers() {
        this.subMenuPoppers.forEach(element => {
        element.hide();
        });
    }}


    const slideUp = (target, duration = ANIMATION_DURATION) => {
    const { parentElement } = target;
    parentElement.classList.remove("open");
    target.style.transitionProperty = "height, margin, padding";
    target.style.transitionDuration = `${duration}ms`;
    target.style.boxSizing = "border-box";
    target.style.height = `${target.offsetHeight}px`;
    target.offsetHeight;
    target.style.overflow = "hidden";
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    window.setTimeout(() => {
        target.style.display = "none";
        target.style.removeProperty("height");
        target.style.removeProperty("padding-top");
        target.style.removeProperty("padding-bottom");
        target.style.removeProperty("margin-top");
        target.style.removeProperty("margin-bottom");
        target.style.removeProperty("overflow");
        target.style.removeProperty("transition-duration");
        target.style.removeProperty("transition-property");
    }, duration);
    };
    const slideDown = (target, duration = ANIMATION_DURATION) => {
    const { parentElement } = target;
    parentElement.classList.add("open");
    target.style.removeProperty("display");
    let { display } = window.getComputedStyle(target);
    if (display === "none") display = "block";
    target.style.display = display;
    const height = target.offsetHeight;
    target.style.overflow = "hidden";
    target.style.height = 0;
    target.style.paddingTop = 0;
    target.style.paddingBottom = 0;
    target.style.marginTop = 0;
    target.style.marginBottom = 0;
    target.offsetHeight;
    target.style.boxSizing = "border-box";
    target.style.transitionProperty = "height, margin, padding";
    target.style.transitionDuration = `${duration}ms`;
    target.style.height = `${height}px`;
    target.style.removeProperty("padding-top");
    target.style.removeProperty("padding-bottom");
    target.style.removeProperty("margin-top");
    target.style.removeProperty("margin-bottom");
    window.setTimeout(() => {
        target.style.removeProperty("height");
        target.style.removeProperty("overflow");
        target.style.removeProperty("transition-duration");
        target.style.removeProperty("transition-property");
    }, duration);
    };

    const slideToggle = (target, duration = ANIMATION_DURATION) => {
    if (window.getComputedStyle(target).display === "none")
    return slideDown(target, duration);
    return slideUp(target, duration);
    };

    const PoppersInstance = new Poppers();

    /**
     * wait for the current animation to finish and update poppers position
     */
    const updatePoppersTimeout = () => {
    setTimeout(() => {
        PoppersInstance.updatePoppers();
    }, ANIMATION_DURATION);
    };

    /**
     * sidebar collapse handler
     */
    document.getElementById("btn-collapse").addEventListener("click", () => {
    SIDEBAR_EL.classList.toggle("collapsed");
    PoppersInstance.closePoppers();
    if (SIDEBAR_EL.classList.contains("collapsed"))
    FIRST_SUB_MENUS_BTN.forEach(element => {
        element.parentElement.classList.remove("open");
    });

    updatePoppersTimeout();
    });

    /**
     * sidebar toggle handler (on break point )
     */
    document.getElementById("btn-toggle").addEventListener("click", () => {
    SIDEBAR_EL.classList.toggle("toggled");

    updatePoppersTimeout();
    });

    /**
     * toggle sidebar on overlay click
     */
    document.getElementById("overlay").addEventListener("click", () => {
    SIDEBAR_EL.classList.toggle("toggled");
    });

    const defaultOpenMenus = document.querySelectorAll(".menu-item.sub-menu.open");

    defaultOpenMenus.forEach(element => {
    element.lastElementChild.style.display = "block";
    });

    /**
     * handle top level submenu click
     */
    FIRST_SUB_MENUS_BTN.forEach(element => {
    element.addEventListener("click", () => {
        if (SIDEBAR_EL.classList.contains("collapsed"))
        PoppersInstance.togglePopper(element.nextElementSibling);else
        {
        const parentMenu = element.closest(".menu.open-current-submenu");
        if (parentMenu)
        parentMenu.
        querySelectorAll(":scope > ul > .menu-item.sub-menu > a").
        forEach(
        (el) =>
        window.getComputedStyle(el.nextElementSibling).display !==
        "none" && slideUp(el.nextElementSibling));

        slideToggle(element.nextElementSibling);
        }
    });
    });

    /**
     * handle inner submenu click
     */
    INNER_SUB_MENUS_BTN.forEach(element => {
    element.addEventListener("click", () => {
        slideToggle(element.nextElementSibling);
    });
    });
  </script>
</html>
