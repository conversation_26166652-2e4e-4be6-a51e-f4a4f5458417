{% extends "base.html" %}

{% block styles %}
  <link rel="stylesheet" href="{{ url_for('static', filename='css/extra.css') }}" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" />
  <style>
    .templates-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 20px;
      transition: all 0.3s ease;
    }
    
    .templates-card:hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .template-header {
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .template-header:hover {
      background-color: #f8f9fa;
    }
    
    .template-summary {
      flex-grow: 1;
    }
    
    .template-name {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 5px;
    }
    
    .template-id {
      font-size: 0.875rem;
      color: #6c757d;
      font-family: monospace;
      margin-bottom: 8px;
    }
    
    .template-identifiers {
      display: flex;
      gap: 5px;
      flex-wrap: wrap;
    }
    
    .identifier-badge {
      background-color: #e9ecef;
      color: #495057;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 500;
    }
    
    .expand-icon {
      font-size: 1.2rem;
      color: #6c757d;
      transition: transform 0.3s ease;
    }
    
    .expand-icon.expanded {
      transform: rotate(180deg);
    }
    
    .template-details {
      display: none;
      padding: 20px;
      border-top: 1px solid #f0f0f0;
      background-color: #fafbfc;
    }
    
    .template-details.show {
      display: block;
    }
    
    .detail-section {
      margin-bottom: 25px;
    }
    
    .detail-section:last-child {
      margin-bottom: 0;
    }
    
    .detail-label {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      font-size: 0.95rem;
    }
    
    .prompt-content {
      background: #fff;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.5;
      color: #333;
    }
    
    .json-container {
      background: #fff;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      overflow: hidden;
    }
    
    .json-content {
      max-height: 400px;
      overflow-y: auto;
      overflow-x: auto;
      font-size: 0.875rem;
      white-space: pre;
    }

    .json-content code {
      white-space: pre-wrap !important;
      word-break: break-word;
      overflow-wrap: break-word;
      display: block;
    }
    
    .no-templates {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
    }
    
    .no-templates i {
      font-size: 3rem;
      margin-bottom: 15px;
      color: #dee2e6;
    }
    
    .templates-header {
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 30px;
      padding-bottom: 15px;
    }
    
    .templates-count {
      background-color: #e3f2fd;
      color: #1976d2;
      padding: 4px 12px;
      border-radius: 16px;
      font-size: 0.875rem;
      font-weight: 500;
      margin-left: 10px;
    }
  </style>
{% endblock %}

{% block scripts %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Handle template expansion
      document.querySelectorAll('.template-header').forEach(header => {
        header.addEventListener('click', function() {
          const card = this.closest('.templates-card');
          const details = card.querySelector('.template-details');
          const icon = this.querySelector('.expand-icon');
          
          if (details.classList.contains('show')) {
            details.classList.remove('show');
            icon.classList.remove('expanded');
          } else {
            details.classList.add('show');
            icon.classList.add('expanded');
            
            // Trigger Prism highlighting for JSON content
            const jsonContent = details.querySelector('.json-content');
            if (jsonContent) {
              Prism.highlightElement(jsonContent);
            }
          }
        });
      });
    });
  </script>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-12">
      <div class="templates-header">
        <h1>
          <i class="ri-file-code-line"></i> 
          Templates Visualization
          {% if templates %}
            <span class="templates-count">{{ templates|length }} template{{ 's' if templates|length != 1 else '' }}</span>
          {% endif %}
        </h1>
        <p class="text-muted">View and explore all available templates from the database</p>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  {% if templates %}
    <div class="row">
      <div class="col-12">
        {% for template in templates %}
          <div class="templates-card">
            <div class="template-header">
              <div class="template-summary">
                <div class="template-name">{{ template['name'] }}</div>
                <div class="template-id">ID: {{ template['id'] }}</div>
                {% if template['type'] %}
                  <div class="template-identifiers">
                    <span class="identifier-badge" style="background-color: #d1ecf1; color: #0c5460;">{{ template['type'] }}</span>
                  </div>
                {% endif %}
              </div>
              <div class="expand-icon">
                <i class="ri-arrow-down-s-line"></i>
              </div>
            </div>
            
            <div class="template-details">
              {% if template['identifiers'] %}
              <div class="detail-section">
                <div class="detail-label">
                  <i class="ri-price-tag-3-line"></i> Identifiers
                </div>
                <div class="template-identifiers">
                  {% for identifier in template['identifiers'] %}
                    <span class="identifier-badge">{{ identifier }}</span>
                  {% endfor %}
                </div>
              </div>
              {% else %}
              <div class="detail-section">
                <div class="detail-label">
                  <i class="ri-price-tag-3-line"></i> Identifiers
                </div>
                <div class="template-identifiers">
                  <span class="identifier-badge" style="background-color: #fff3cd; color: #856404;">No identifiers</span>
                </div>
              </div>
              {% endif %}

              <div class="detail-section">
                <div class="detail-label">
                  <i class="ri-chat-3-line"></i> Prompt
                </div>
                <div class="prompt-content">
                  {{ template['prompt'] }}
                </div>
              </div>

              <div class="detail-section">
                <div class="detail-label">
                  <i class="ri-code-s-slash-line"></i> Response Schema
                </div>
                <div class="json-container">
                  <pre class="json-content"><code class="language-json">{{ template['response_schema'] | tojson(indent=2) | replace('\\u0027', "'") | safe }}</code></pre>
                </div>
              </div>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
  {% else %}
    <div class="row">
      <div class="col-12">
        <div class="no-templates">
          <i class="ri-file-search-line"></i>
          <h3>No Templates Found</h3>
          <p>There are currently no templates available in the database.</p>
        </div>
      </div>
    </div>
  {% endif %}
</div>
{% endblock %}
